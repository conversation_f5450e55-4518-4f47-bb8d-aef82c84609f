#!/usr/bin/env python3
"""
Script para convertir el dataset de MP3 a WAV con formato específico para entrenamiento de TTS
- Convierte MP3 a WAV (16kHz o 22kHz, 16-bit, mono)
- Renombra archivos secuencialmente (1.wav, 2.wav, etc.)
- Genera archivo de transcripción en formato requerido
"""

import json
import os
import subprocess
import sys
from pathlib import Path

def load_dataset(json_file):
    """Cargar el dataset JSON"""
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data['processed_texts']

def convert_mp3_to_wav(input_file, output_file, sample_rate=16000):
    """
    Convertir MP3 a WAV usando ffmpeg
    Args:
        input_file: archivo MP3 de entrada
        output_file: archivo WAV de salida
        sample_rate: frecuencia de muestreo (16000 o 22050)
    """
    cmd = [
        'ffmpeg',
        '-i', input_file,
        '-ar', str(sample_rate),  # Sample rate
        '-ac', '1',               # Mono (1 channel)
        '-sample_fmt', 's16',     # 16-bit
        '-y',                     # Overwrite output file
        output_file
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error converting {input_file}: {e}")
        print(f"stderr: {e.stderr}")
        return False

def main():
    # Configuración
    json_file = 'progress_2_completed.json'
    output_dir = 'wavs'
    transcription_file = 'transcription.txt'
    sample_rate = 16000  # Cambiar a 22050 si prefieres
    
    print(f"🎵 CONVERSIÓN DE DATASET MP3 → WAV")
    print(f"=" * 50)
    print(f"Frecuencia de muestreo: {sample_rate} Hz")
    print(f"Formato: 16-bit, mono")
    print(f"Directorio de salida: {output_dir}/")
    print(f"Archivo de transcripción: {transcription_file}")
    
    # Crear directorio de salida
    os.makedirs(output_dir, exist_ok=True)
    
    # Cargar dataset
    print(f"\n📂 Cargando dataset...")
    try:
        processed_texts = load_dataset(json_file)
        print(f"   Encontradas {len(processed_texts)} entradas")
    except Exception as e:
        print(f"❌ Error cargando dataset: {e}")
        return 1
    
    # Filtrar solo archivos no omitidos
    valid_entries = {k: v for k, v in processed_texts.items() 
                    if not v.get('skipped', False) and 'filepath' in v and 'text' in v}
    
    print(f"   Archivos válidos para conversión: {len(valid_entries)}")
    
    # Ordenar por ID para consistencia
    sorted_entries = sorted(valid_entries.items())
    
    # Preparar listas para conversión y transcripción
    conversions = []
    transcriptions = []
    
    print(f"\n🔄 Preparando conversiones...")
    for i, (audio_id, data) in enumerate(sorted_entries, 1):
        input_file = data['filepath']
        output_file = os.path.join(output_dir, f"{i}.wav")
        text = data['text'].strip()
        
        # Verificar que el archivo MP3 existe
        if not os.path.exists(input_file):
            print(f"⚠️  Archivo no encontrado: {input_file}")
            continue
            
        conversions.append((input_file, output_file, i))
        transcriptions.append(f"{output_file}|{text}")
    
    print(f"   Archivos a convertir: {len(conversions)}")
    
    # Realizar conversiones
    print(f"\n🎧 Convirtiendo archivos...")
    successful = 0
    failed = 0
    
    for input_file, output_file, number in conversions:
        print(f"   [{number:4d}/{len(conversions)}] {os.path.basename(input_file)} → {os.path.basename(output_file)}")
        
        if convert_mp3_to_wav(input_file, output_file, sample_rate):
            successful += 1
        else:
            failed += 1
            print(f"      ❌ Falló la conversión")
    
    # Generar archivo de transcripción
    print(f"\n📝 Generando archivo de transcripción...")
    try:
        with open(transcription_file, 'w', encoding='utf-8') as f:
            for line in transcriptions[:successful]:  # Solo incluir archivos convertidos exitosamente
                f.write(line + '\n')
        print(f"   ✅ Archivo creado: {transcription_file}")
    except Exception as e:
        print(f"   ❌ Error creando transcripción: {e}")
    
    # Resumen final
    print(f"\n📊 RESUMEN:")
    print(f"=" * 50)
    print(f"Conversiones exitosas: {successful}")
    print(f"Conversiones fallidas: {failed}")
    print(f"Total procesado: {successful + failed}")
    print(f"Directorio de salida: {output_dir}/")
    print(f"Archivo de transcripción: {transcription_file}")
    
    if successful > 0:
        print(f"\n✅ Dataset listo para entrenamiento de TTS!")
        print(f"   Formato: WAV {sample_rate}Hz, 16-bit, mono")
        print(f"   Archivos: 1.wav hasta {successful}.wav")
    
    return 0 if failed == 0 else 1

if __name__ == "__main__":
    sys.exit(main())
